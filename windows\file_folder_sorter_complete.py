#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件/文件夹排序工具 - 精简版
支持拖拽排序和批量重命名
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import re
from pathlib import Path
from tkinterdnd2 import DND_FILES, TkinterDnD

def natural_sort_key(text):
    """自然排序键函数"""
    def convert(text):
        return int(text) if text.isdigit() else text.lower()
    return [convert(c) for c in re.split(r'(\d+)', str(text))]

class FileFolderSorter:
    def __init__(self, root):
        self.root = root
        self.root.title("文件/文件夹排序工具")
        self.root.geometry("700x500")

        # 数据存储
        self.items = []
        self.batch_items = []

        # 拖拽变量
        self.drag_item = None
        self.drag_active = False

        self.setup_ui()
        self.setup_drag_drop()

    def setup_ui(self):
        """设置界面"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.setup_sort_tab()
        self.setup_batch_tab()

        # 状态栏
        self.status_label = tk.Label(self.root, text="准备就绪",
                                   bg="lightgray", relief="sunken", anchor="w")
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)

    def setup_sort_tab(self):
        """设置排序标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="拖拽排序")

        main_frame = ttk.Frame(frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=0, column=0, sticky="nw", padx=(0, 10))

        ttk.Button(btn_frame, text="添加文件", command=self.add_files).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="添加文件夹", command=self.add_folders).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="清空", command=self.clear_list).pack(fill=tk.X, pady=2)

        self.remove_prefix_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="去除序号前缀",
                       variable=self.remove_prefix_var,
                       command=self.refresh_sort).pack(fill=tk.X, pady=2)

        ttk.Separator(btn_frame, orient='horizontal').pack(fill=tk.X, pady=5)
        ttk.Button(btn_frame, text="保存重命名", command=self.save_changes).pack(fill=tk.X, pady=2)

        # 列表区域
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=0, column=1, rowspan=2, sticky="nsew")
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        self.tree = ttk.Treeview(list_frame, columns=('new_name',), show='tree headings')
        self.tree.heading('#0', text='原始名称')
        self.tree.heading('new_name', text='新名称')
        self.tree.column('#0', width=200)
        self.tree.column('new_name', width=250)

        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

        # 拖拽事件
        self.tree.bind('<Button-1>', self.on_click)
        self.tree.bind('<B1-Motion>', self.on_drag_motion)
        self.tree.bind('<ButtonRelease-1>', self.on_drop)

    def setup_batch_tab(self):
        """设置批量重命名标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="批量重命名")

        main_frame = ttk.Frame(frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=0, column=0, sticky="nw", padx=(0, 10))

        ttk.Button(btn_frame, text="添加文件", command=self.batch_add_files).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="清空", command=self.batch_clear).pack(fill=tk.X, pady=2)

        self.batch_remove_prefix_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="去除序号前缀",
                       variable=self.batch_remove_prefix_var,
                       command=self.batch_sort).pack(fill=tk.X, pady=2)

        ttk.Separator(btn_frame, orient='horizontal').pack(fill=tk.X, pady=5)
        ttk.Button(btn_frame, text="复制文件名", command=self.copy_names).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="粘贴文件名", command=self.paste_names).pack(fill=tk.X, pady=2)
        ttk.Separator(btn_frame, orient='horizontal').pack(fill=tk.X, pady=5)
        ttk.Button(btn_frame, text="保存重命名", command=self.batch_save).pack(fill=tk.X, pady=2)

        # 文本编辑区域
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=0, column=1, sticky="nsew")
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        self.batch_text = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 10))

        v_scroll = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.batch_text.yview)
        h_scroll = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.batch_text.xview)
        self.batch_text.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)

        self.batch_text.grid(row=0, column=0, sticky="nsew")
        v_scroll.grid(row=0, column=1, sticky="ns")
        h_scroll.grid(row=1, column=0, sticky="ew")

    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_system_drop)

    def remove_number_prefix(self, name):
        """去除数字前缀"""
        return re.sub(r'^(\d+[\.\-]\s*)', '', name).strip()

    # 排序功能
    def add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(title="选择文件")
        if files:
            for file_path in files:
                self.add_item(file_path)
            self.refresh_sort()

    def add_folders(self):
        """添加文件夹"""
        folder = filedialog.askdirectory(title="选择文件夹")
        if folder:
            self.add_item(folder)
            self.refresh_sort()

    def add_item(self, path):
        """添加项目"""
        path_obj = Path(path)
        if not path_obj.exists() or any(item['path'] == path for item in self.items):
            return False

        self.items.append({
            'path': path,
            'original_name': path_obj.name,
            'display_name': path_obj.name,
            'is_folder': path_obj.is_dir()
        })
        return True

    def clear_list(self):
        """清空列表"""
        self.items.clear()
        self.refresh_tree()

    def refresh_sort(self):
        """重新排序"""
        if not self.items:
            return

        # 分离文件夹和文件
        folders = [item for item in self.items if item['is_folder']]
        files = [item for item in self.items if not item['is_folder']]

        # 排序
        if self.remove_prefix_var.get():
            folders.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
            files.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
        else:
            folders.sort(key=lambda x: x['original_name'].lower())
            files.sort(key=lambda x: x['original_name'].lower())

        self.items = folders + files

        # 添加序号
        for i, item in enumerate(self.items, 1):
            clean_name = self.remove_number_prefix(item['original_name'])
            item['display_name'] = f"{i}. {clean_name}"

        self.refresh_tree()

    def refresh_tree(self):
        """刷新显示"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        for item in self.items:
            icon = "📁" if item['is_folder'] else "📄"
            self.tree.insert('', 'end', text=f"{icon} {item['original_name']}",
                           values=(item['display_name'],))

    # 拖拽事件
    def on_click(self, event):
        """点击事件"""
        item = self.tree.identify_row(event.y)
        if item:
            self.drag_item = item
            self.drag_active = False

    def on_drag_motion(self, event):
        """拖拽移动"""
        if not self.drag_item:
            return

        self.drag_active = True
        target_item = self.tree.identify_row(event.y)

        if target_item and target_item != self.drag_item:
            # 获取索引
            children = self.tree.get_children()
            drag_idx = list(children).index(self.drag_item)
            target_idx = list(children).index(target_item)

            # 重新排列
            item = self.items.pop(drag_idx)
            self.items.insert(target_idx, item)

            # 更新显示序号
            for i, item in enumerate(self.items, 1):
                clean_name = self.remove_number_prefix(item['original_name'])
                item['display_name'] = f"{i}. {clean_name}"

            self.refresh_tree()
            self.drag_item = None

    def on_drop(self, event):
        """放下事件"""
        self.drag_item = None
        self.drag_active = False

    def save_changes(self):
        """保存重命名"""
        if not self.items:
            messagebox.showwarning("警告", "没有文件需要重命名")
            return

        success_count = 0
        error_count = 0

        for item in self.items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['display_name']

                if old_path != new_path:
                    old_path.rename(new_path)
                    success_count += 1

            except Exception as e:
                error_count += 1
                print(f"重命名失败: {e}")

        if error_count == 0:
            messagebox.showinfo("成功", f"成功重命名 {success_count} 个文件/文件夹")
            self.clear_list()
        else:
            messagebox.showwarning("部分成功", f"成功 {success_count} 个，失败 {error_count} 个")

    # 批量重命名功能
    def batch_add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(title="选择文件")
        if files:
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    self.batch_items.append({
                        'path': file_path,
                        'original_name': path_obj.name,
                        'new_name': path_obj.name
                    })
            self.batch_sort()
            self.update_batch_text()

    def batch_clear(self):
        """清空列表"""
        self.batch_items.clear()
        self.batch_text.delete(1.0, tk.END)

    def batch_sort(self):
        """排序文件"""
        if self.batch_remove_prefix_var.get():
            self.batch_items.sort(key=lambda x: self.remove_number_prefix(x['original_name']).lower())
        else:
            self.batch_items.sort(key=lambda x: x['original_name'].lower())

        for i, item in enumerate(self.batch_items, 1):
            clean_name = self.remove_number_prefix(item['original_name'])
            item['new_name'] = f"{i}. {clean_name}"

        self.update_batch_text()

    def update_batch_text(self):
        """更新文本内容"""
        self.batch_text.delete(1.0, tk.END)
        for item in self.batch_items:
            self.batch_text.insert(tk.END, item['new_name'] + '\n')

    def copy_names(self):
        """复制文件名"""
        if not self.batch_items:
            messagebox.showwarning("警告", "没有文件")
            return

        names = '\n'.join(item['new_name'] for item in self.batch_items)
        self.root.clipboard_clear()
        self.root.clipboard_append(names)
        messagebox.showinfo("成功", f"已复制 {len(self.batch_items)} 个文件名")

    def paste_names(self):
        """粘贴文件名"""
        try:
            content = self.root.clipboard_get()
            lines = [line.strip() for line in content.split('\n') if line.strip()]

            if len(lines) != len(self.batch_items):
                messagebox.showerror("错误", f"数量不匹配！当前：{len(self.batch_items)}，粘贴：{len(lines)}")
                return

            for i, line in enumerate(lines):
                if i < len(self.batch_items):
                    self.batch_items[i]['new_name'] = line

            self.update_batch_text()
            messagebox.showinfo("成功", f"已更新 {len(lines)} 个文件名")

        except tk.TclError:
            messagebox.showerror("错误", "剪贴板为空")

    def batch_save(self):
        """保存批量重命名"""
        if not self.batch_items:
            messagebox.showwarning("警告", "没有文件")
            return

        # 获取编辑器内容
        content = self.batch_text.get(1.0, tk.END).strip()
        lines = [line.strip() for line in content.split('\n') if line.strip()]

        if len(lines) != len(self.batch_items):
            messagebox.showerror("错误", f"数量不匹配！文件：{len(self.batch_items)}，编辑器：{len(lines)}")
            return

        # 更新文件名
        for i, line in enumerate(lines):
            if i < len(self.batch_items):
                self.batch_items[i]['new_name'] = line

        # 执行重命名
        success_count = 0
        error_count = 0

        for item in self.batch_items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['new_name']

                if old_path != new_path:
                    old_path.rename(new_path)
                    success_count += 1

            except Exception as e:
                error_count += 1
                print(f"重命名失败: {e}")

        if error_count == 0:
            messagebox.showinfo("成功", f"成功重命名 {success_count} 个文件")
            self.batch_clear()
        else:
            messagebox.showwarning("部分成功", f"成功 {success_count} 个，失败 {error_count} 个")

    def on_system_drop(self, event):
        """处理系统拖拽"""
        try:
            files = self.root.tk.splitlist(event.data)
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")

            if tab_text == "批量重命名":
                for file_path in files:
                    path_obj = Path(file_path)
                    if path_obj.exists() and path_obj.is_file():
                        self.batch_items.append({
                            'path': file_path,
                            'original_name': path_obj.name,
                            'new_name': path_obj.name
                        })
                self.batch_sort()
                self.update_batch_text()
                self.status_label.config(text=f"已添加 {len(files)} 个文件", bg="lightgreen")
            else:
                for file_path in files:
                    self.add_item(file_path)
                self.refresh_sort()
                self.status_label.config(text=f"已添加 {len(files)} 个项目", bg="lightgreen")

            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

        except Exception as e:
            self.status_label.config(text=f"拖拽失败: {str(e)}", bg="lightcoral")

    # 树形管理功能方法
    def on_tree_folder_drop(self, event):
        """处理拖拽到文件夹树的事件"""
        try:
            files = self.root.tk.splitlist(event.data)
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists() and path_obj.is_dir():
                    self.add_folder_to_tree(file_path)
            self.status_label.config(text=f"已添加 {len([f for f in files if Path(f).is_dir()])} 个文件夹", bg="lightgreen")
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))
        except Exception as e:
            self.status_label.config(text=f"添加文件夹失败: {str(e)}", bg="lightcoral")

    def on_tree_file_drop(self, event):
        """处理拖拽到文件列表的事件"""
        try:
            files = self.root.tk.splitlist(event.data)
            added_count = 0
            start_index = self.files_listbox.size()  # 记录开始添加的位置

            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists():
                    # 添加到文件列表，显示完整路径
                    self.files_listbox.insert(tk.END, file_path)
                    added_count += 1

            # 自动选中新添加的文件
            if added_count > 0:
                self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
                for i in range(start_index, start_index + added_count):
                    self.files_listbox.selection_set(i)  # 选中新添加的文件

            self.status_label.config(text=f"已添加并选中 {added_count} 个文件/文件夹", bg="lightgreen")
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))
        except Exception as e:
            self.status_label.config(text=f"添加文件失败: {str(e)}", bg="lightcoral")

    def add_folder_to_tree(self, folder_path):
        """添加文件夹到树形结构"""
        path_obj = Path(folder_path)
        if not path_obj.exists() or not path_obj.is_dir():
            return

        # 检查是否已存在
        for item in self.tree_folders:
            if item == folder_path:
                return

        self.tree_folders.append(folder_path)

        # 重新构建整个树形结构以确保正确排序
        self.rebuild_folder_tree()

    def rebuild_folder_tree(self):
        """重新构建文件夹树形结构，确保正确排序"""
        # 清空当前树形结构
        self.folder_tree.delete(*self.folder_tree.get_children())

        # 对根级文件夹进行自然排序
        sorted_folders = sorted(self.tree_folders, key=lambda x: natural_sort_key(Path(x).name))

        # 重新添加所有文件夹
        for folder_path in sorted_folders:
            path_obj = Path(folder_path)
            folder_name = path_obj.name
            parent_id = self.folder_tree.insert('', 'end', text=f"📁 {folder_name}",
                                               values=(folder_path,), open=True)

            # 递归添加子文件夹
            self.add_subfolders_to_tree(folder_path, parent_id)

    def add_subfolders_to_tree(self, parent_path, parent_id):
        """递归添加子文件夹"""
        try:
            parent_obj = Path(parent_path)
            # 获取所有子文件夹并按自然排序
            subdirs = [item for item in parent_obj.iterdir() if item.is_dir()]
            subdirs.sort(key=lambda x: natural_sort_key(x.name))  # 使用自然排序

            for item in subdirs:
                folder_name = item.name
                child_id = self.folder_tree.insert(parent_id, 'end',
                                                  text=f"📁 {folder_name}",
                                                  values=(str(item),), open=False)
                # 递归添加子文件夹
                self.add_subfolders_to_tree(str(item), child_id)
        except PermissionError:
            # 跳过无权限访问的文件夹
            pass
        except Exception as e:
            print(f"添加子文件夹时出错: {e}")

    def on_tree_double_click(self, event):
        """双击文件夹树项目"""
        selection = self.folder_tree.selection()
        if selection:
            item = selection[0]
            values = self.folder_tree.item(item, 'values')
            if values:
                folder_path = values[0]
                # 在文件管理器中打开文件夹
                try:
                    os.startfile(folder_path)
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")

    def on_file_double_click(self, event):
        """双击文件列表项目"""
        selection = self.files_listbox.curselection()
        if selection:
            file_path = self.files_listbox.get(selection[0])
            # 在文件管理器中打开文件或文件夹
            try:
                os.startfile(file_path)
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {str(e)}")

    def tree_clear_folders(self):
        """清空文件夹树"""
        self.tree_folders.clear()
        self.folder_tree.delete(*self.folder_tree.get_children())
        self.status_label.config(text="已清空文件夹树", bg="lightblue")
        self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def tree_clear_files(self):
        """清空文件列表"""
        self.files_listbox.delete(0, tk.END)
        self.status_label.config(text="已清空文件列表", bg="lightblue")
        self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def tree_move_files(self):
        """移动选中的文件到选中的文件夹"""
        # 获取选中的文件夹
        tree_selection = self.folder_tree.selection()
        if not tree_selection:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return

        target_folder = self.folder_tree.item(tree_selection[0], 'values')[0]
        if not Path(target_folder).exists():
            messagebox.showerror("错误", "目标文件夹不存在")
            return

        # 获取选中的文件
        file_selection = self.files_listbox.curselection()
        if not file_selection:
            messagebox.showwarning("警告", "请先选择要移动的文件")
            return

        # 确认移动
        file_count = len(file_selection)
        if not messagebox.askyesno("确认", f"确定要移动 {file_count} 个文件/文件夹到\n{target_folder} 吗？"):
            return

        success_count = 0
        error_count = 0

        # 从后往前删除，避免索引变化
        for i in reversed(file_selection):
            source_path = self.files_listbox.get(i)
            source_obj = Path(source_path)

            try:
                if source_obj.exists():
                    target_path = Path(target_folder) / source_obj.name

                    # 如果目标已存在，询问是否覆盖
                    if target_path.exists():
                        if not messagebox.askyesno("文件已存在",
                                                 f"目标位置已存在 {source_obj.name}\n是否覆盖？"):
                            continue
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()

                    # 移动文件/文件夹
                    shutil.move(str(source_obj), str(target_path))
                    self.files_listbox.delete(i)
                    success_count += 1
                else:
                    error_count += 1

            except Exception as e:
                error_count += 1
                print(f"移动文件失败: {e}")

        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功移动 {success_count} 个文件/文件夹")
            self.status_label.config(text=f"成功移动 {success_count} 个文件", bg="lightgreen")
        else:
            messagebox.showwarning("部分成功", f"成功移动 {success_count} 个文件/文件夹\n失败 {error_count} 个文件/文件夹")
            self.status_label.config(text=f"移动完成，{error_count} 个失败", bg="orange")

        self.root.after(3000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

    def setup_system_drag_drop(self):
        """设置系统拖拽功能"""
        self.root.drop_target_register(DND_FILES)
        self.root.dnd_bind('<<Drop>>', self.on_system_drop)

    def on_system_drop(self, event):
        """处理从系统拖拽的文件/文件夹"""
        try:
            files = self.root.tk.splitlist(event.data)
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")

            if tab_text == "批量重命名":
                # 添加到批量重命名列表
                for file_path in files:
                    path_obj = Path(file_path)
                    if path_obj.exists() and path_obj.is_file():
                        self.batch_items.append({
                            'path': file_path,
                            'original_name': path_obj.name,
                            'new_name': path_obj.name
                        })
                self.batch_sort_files()
                self.update_batch_text()
                self.status_label.config(text=f"已添加 {len(files)} 个文件到批量重命名", bg="lightgreen")
            elif tab_text == "树形管理":
                # 树形管理功能
                folder_count = 0
                file_count = 0
                start_index = self.files_listbox.size()  # 记录开始添加文件的位置

                for file_path in files:
                    path_obj = Path(file_path)
                    if path_obj.exists():
                        if path_obj.is_dir():
                            self.add_folder_to_tree(file_path)
                            folder_count += 1
                        else:
                            self.files_listbox.insert(tk.END, file_path)
                            file_count += 1

                # 自动选中新添加的文件
                if file_count > 0:
                    self.files_listbox.selection_clear(0, tk.END)  # 清除之前的选择
                    for i in range(start_index, start_index + file_count):
                        self.files_listbox.selection_set(i)  # 选中新添加的文件

                self.status_label.config(text=f"已添加 {folder_count} 个文件夹，{file_count} 个文件并自动选中", bg="lightgreen")
            else:
                # 拖拽排序功能
                added_any = False
                for file_path in files:
                    if self.add_item(file_path):
                        added_any = True
                if added_any:
                    self.auto_reorder_items()
                    self.status_label.config(text=f"已添加 {len(files)} 个项目", bg="lightgreen")

            # 2秒后恢复状态栏
            self.root.after(2000, lambda: self.status_label.config(text="准备就绪", bg="lightgray"))

        except Exception as e:
            self.status_label.config(text=f"拖拽失败: {str(e)}", bg="lightcoral")

def main():
    root = TkinterDnD.Tk()
    app = FileFolderSorterComplete(root)
    root.mainloop()

if __name__ == "__main__":
    main()
